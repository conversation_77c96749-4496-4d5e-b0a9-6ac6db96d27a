// Simple Express server for bansheeblast Library API
const express = require('express');
const app = express();
const cors = require('cors');  // Import the cors middleware
const PORT = process.env.PORT || 3001;

app.use(cors());  // Enable CORS for all routes

app.use(express.json());

// Example in-memory data (replace with DB later)
let userLibrary = {
  playlists: [
    { id: 1, name: 'Favorites', tracks: [101, 102] },
    { id: 2, name: 'Chill Vibes', tracks: [103] },
  ],
  likedSongs: [101, 104, 105],
};

// Get all playlists
app.get('/api/library', (req, res) => {
    res.json(userLibrary);
  });


// Get all playlists
app.get('/api/library/playlists', (req, res) => {
  res.json(userLibrary.playlists);
});

// Get liked songs
app.get('/api/library/liked', (req, res) => {
  res.json(userLibrary.likedSongs);
});

// Add a new playlist
app.post('/api/library/playlists', (req, res) => {
  const { name } = req.body;
  const newPlaylist = { id: Date.now(), name, tracks: [] };
  userLibrary.playlists.push(newPlaylist);
  res.status(201).json(newPlaylist);
});

// Delete a playlist
app.delete('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const index = userLibrary.playlists.findIndex(p => p.id === id);
  if (index !== -1) {
    userLibrary.playlists.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add/Remove a song to liked songs
app.post('/api/library/liked/:songId', (req, res) => {
    const songId = parseInt(req.params.songId);
    if (!userLibrary.likedSongs.includes(songId)) {
      userLibrary.likedSongs.push(songId);
      res.status(201).json({ success: true, liked: true, songId });
    } else {
        removeLikedSong(songId, res)
    }
// Remove a song from liked songs
app.delete('/api/library/liked/:songId', (req, res) => {
  const songId = parseInt(req.params.songId);
  const idx = userLibrary.likedSongs.indexOf(songId);
  if (idx !== -1) {
    userLibrary.likedSongs.splice(idx, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Song not found in liked songs' });
  }
  
});

function removeLikedSong(songId, res) {
    const idx = userLibrary.likedSongs.indexOf(songId);
    if (idx !== -1) {
      userLibrary.likedSongs.splice(idx, 1);
      res.json({ success: true, liked: false, songId });
    } else {
      res.status(404).json({ error: 'Song not found in liked songs' });
    }
}
});

// Edit a playlist name
app.put('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const { name } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist && name) {
    playlist.name = name;
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found or invalid name' });
  }
});

// Get a single playlist by ID
app.get('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add a track to a playlist
app.post('/api/library/playlists/:id/tracks', (req, res) => {
  const id = parseInt(req.params.id);
  const { trackId } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist && trackId) {
    if (!playlist.tracks.includes(trackId)) {
      playlist.tracks.push(trackId);
    }
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found or invalid trackId' });
  }
});

// Remove a track from a playlist
app.delete('/api/library/playlists/:id/tracks/:trackId', (req, res) => {
  const id = parseInt(req.params.id);
  const trackId = parseInt(req.params.trackId);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    const idx = playlist.tracks.indexOf(trackId);
    if (idx !== -1) {
      playlist.tracks.splice(idx, 1);
      res.json({ success: true, playlist });
    } else {
      res.status(404).json({ error: 'Track not found in playlist' });
    }
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

app.listen(PORT, () => {
  console.log(`Library API server running on port ${PORT}`);
});
