# 🎵 BansheeBlast Demo - Quick Start Guide

## 🚀 Getting Started (Super Simple!)

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Start the Backend Server
```bash
npm start
```
You should see: `Library API server running on port 3001`

### Step 3: Open the Library Page
1. Open `library.html` in your browser
2. The page will automatically connect to your backend!

## ✨ What You Can Do (Demo Features)

### 📚 Library Page Features:
- ✅ **View Playlists** - See your playlists loaded from the backend
- ✅ **View Liked Songs** - See your liked songs with real track data
- ✅ **Create Playlist** - Click any "Edit" button and enter a new name
- ✅ **Delete Playlist** - Click "Delete" button (with confirmation)
- ✅ **Real-time Updates** - Changes are saved and reflected immediately

### 🔧 Backend API Endpoints:
- `GET /api/library/playlists` - Get all playlists
- `GET /api/library/liked` - Get liked songs with track details
- `POST /api/library/playlists` - Create new playlist
- `PUT /api/library/playlists/:id` - Edit playlist name
- `DELETE /api/library/playlists/:id` - Delete playlist
- `DELETE /api/library/liked/:songId` - Unlike a song

## 🎯 Demo Data Included:
- **3 Sample Playlists**: Favorites, Chill Vibes, Workout Mix
- **6 Sample Tracks**: With realistic titles, artists, albums, and durations
- **3 Liked Songs**: Pre-loaded for demonstration

## 🔍 How to Test:

1. **Open Browser Console** (F12) to see backend communication
2. **Try Creating a Playlist**: 
   - Click any "Edit" button
   - Enter a new playlist name
   - Watch it appear instantly!
3. **Try Deleting a Playlist**:
   - Click "Delete" button
   - Confirm the action
   - See it disappear from the UI

## 🛠️ Troubleshooting:

**If you see "Unable to connect to server":**
- Make sure you ran `npm start` and see the server running message
- Check that port 3001 is not being used by another app
- Refresh the library.html page

**If playlists don't appear:**
- Check browser console for errors
- Make sure the server is running on http://localhost:3001

## 🎉 Success Indicators:
- ✅ Console shows "Library data loaded successfully!"
- ✅ You see playlists and liked songs on the page
- ✅ Green notification appears: "Library loaded successfully!"
- ✅ Creating/deleting playlists works instantly

## 📝 Next Steps for Full Development:
1. Replace in-memory data with a real database (MongoDB, PostgreSQL)
2. Add user authentication
3. Implement file upload for music
4. Add search functionality
5. Connect to music streaming APIs

---
**You now have a working demo with real backend functionality! 🎵**
