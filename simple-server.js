// Super Simple BansheeBlast Server
const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8080;

// Middleware
app.use(cors());
app.use(express.static('.'));
app.use(express.json());

// In-memory data
let playlists = [
  { id: 1, name: 'Favorites', tracks: [101, 102, 104] },
  { id: 2, name: 'Chill Vibes', tracks: [103, 105] },
  { id: 3, name: 'Workout Mix', tracks: [106, 101] }
];

let likedSongs = [101, 102, 103];
let nextId = 4;

const tracks = {
  101: { id: 101, title: "Midnight Dreams", artist: "Luna Echo", album: "Neon Nights", duration: "3:45", cover: "imgs/album-01.png" },
  102: { id: 102, title: "Ocean Waves", artist: "Cosmic Drift", album: "Deep Space", duration: "4:12", cover: "imgs/album-02.png" },
  103: { id: 103, title: "City Lights", artist: "Neon Pulse", album: "Urban Vibes", duration: "3:28", cover: "imgs/album-03-B.png" },
  104: { id: 104, title: "Starfall", artist: "Galaxy Dreams", album: "Celestial", duration: "5:03", cover: "imgs/album-04-B.png" },
  105: { id: 105, title: "Electric Soul", artist: "Synth Wave", album: "Digital Hearts", duration: "4:21", cover: "imgs/album-01.png" },
  106: { id: 106, title: "Neon Rain", artist: "Cyber City", album: "Future Noir", duration: "3:56", cover: "imgs/album-02.png" }
};

// API Routes
app.get('/api/library/playlists', (req, res) => {
  console.log('📋 GET playlists');
  res.json(playlists);
});

app.get('/api/library/liked', (req, res) => {
  console.log('❤️ GET liked songs');
  const liked = likedSongs.map(id => tracks[id]).filter(Boolean);
  res.json(liked);
});

app.post('/api/library/playlists', (req, res) => {
  console.log('➕ CREATE playlist:', req.body.name);
  const newPlaylist = { id: nextId++, name: req.body.name, tracks: [] };
  playlists.push(newPlaylist);
  res.json(newPlaylist);
});

app.put('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  console.log('✏️ EDIT playlist:', id);
  const playlist = playlists.find(p => p.id === id);
  if (playlist) {
    playlist.name = req.body.name;
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

app.delete('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  console.log('🗑️ DELETE playlist:', id);
  const index = playlists.findIndex(p => p.id === id);
  if (index !== -1) {
    playlists.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

app.get('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const playlist = playlists.find(p => p.id === id);
  if (playlist) {
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

app.delete('/api/library/playlists/:id/tracks/:trackId', (req, res) => {
  const playlistId = parseInt(req.params.id);
  const trackId = parseInt(req.params.trackId);
  const playlist = playlists.find(p => p.id === playlistId);
  if (playlist) {
    const index = playlist.tracks.indexOf(trackId);
    if (index !== -1) {
      playlist.tracks.splice(index, 1);
    }
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

app.delete('/api/library/liked/:songId', (req, res) => {
  const songId = parseInt(req.params.songId);
  const index = likedSongs.indexOf(songId);
  if (index !== -1) {
    likedSongs.splice(index, 1);
  }
  res.json({ success: true });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🎵 BansheeBlast Server running on http://localhost:${PORT}`);
  console.log(`📱 Library Page: http://localhost:${PORT}/library.html`);
  console.log(`🔧 Press Ctrl+C to stop`);
});

// Keep alive
setInterval(() => {
  console.log('💓 Server heartbeat...');
}, 30000);

process.on('SIGINT', () => {
  console.log('\n👋 Shutting down server...');
  server.close(() => {
    console.log('✅ Server stopped');
    process.exit(0);
  });
});
