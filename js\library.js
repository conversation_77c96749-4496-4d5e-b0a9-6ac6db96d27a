// Enhanced Library Page Management System
class LibraryManager {
    constructor() {
        this.searchTimeout = null;
        this.currentFilter = 'all';
        this.sortOrder = 'recent';
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.initializeParticles();
        this.animateStats();
        this.setupIntersectionObserver();
    }

    bindElements() {
        // Search elements
        this.searchInput = document.getElementById('librarySearch');
        this.searchClear = document.getElementById('libraryClear');

        // Navigation tabs
        this.navTabs = document.querySelectorAll('.nav-tab');

        // Action buttons
        this.shuffleAllBtn = document.getElementById('shuffleAllBtn');
        this.importMusicBtn = document.getElementById('importMusicBtn');
        this.createPlaylistBtn = document.querySelector('.create-playlist-btn');
        this.playAllBtn = document.querySelector('.play-all-btn');

        // Statistics elements
        this.statNumbers = document.querySelectorAll('.stat-number');

        // Cards
        this.libraryCards = document.querySelectorAll('.library-card, .playlist-card, .liked-song-card, .artist-card');

        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region') || this.createLiveRegion();
    }

    bindEvents() {
        // Search functionality
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        if (this.searchClear) {
            this.searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // Navigation tabs
        this.navTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const section = e.target.dataset.section;
                this.switchSection(section);
            });
        });

        // Action buttons
        if (this.shuffleAllBtn) {
            this.shuffleAllBtn.addEventListener('click', () => {
                this.shuffleAll();
            });
        }

        if (this.importMusicBtn) {
            this.importMusicBtn.addEventListener('click', () => {
                this.importMusic();
            });
        }

        if (this.createPlaylistBtn) {
            this.createPlaylistBtn.addEventListener('click', () => {
                this.createPlaylist();
            });
        }

        if (this.playAllBtn) {
            this.playAllBtn.addEventListener('click', () => {
                this.playAllLikedSongs();
            });
        }

        // Card interactions
        this.libraryCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.handleCardClick(e, card);
            });

            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleCardClick(e, card);
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.searchInput?.focus();
            }
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.createPlaylist();
            }
        });
    }

    initializeParticles() {
        const particles = document.querySelectorAll('.particles-container .particle');
        particles.forEach((particle, index) => {
            // Random position within the container
            particle.style.top = `${Math.random() * 90 + 2}%`;
            particle.style.left = `${Math.random() * 90 + 2}%`;

            // Random animation delay and duration for variety
            particle.style.animationDelay = `${Math.random() * 10}s`;
            const duration = 8 + Math.random() * 8;
            particle.style.animationDuration = `${duration}s`;
            particle.style.animationName = 'particleDrift';
            particle.style.animationTimingFunction = 'linear';
            particle.style.animationIterationCount = 'infinite';

            // Random drift direction and distance
            const angle = Math.random() * 2 * Math.PI;
            const distance = 30 + Math.random() * 40; // px
            const dx = Math.cos(angle) * distance;
            const dy = Math.sin(angle) * distance;
            particle.style.setProperty('--dx', `${dx}px`);
            particle.style.setProperty('--dy', `${dy}px`);

            // Add different colors for variety
            const colors = ['var(--neon-blue)', 'var(--cosmic-pink)', 'var(--electric-violet)', 'var(--cyber-lime)'];
            const color = colors[index % colors.length];
            particle.style.background = `radial-gradient(circle, ${color}, transparent)`;
        });
    }

    animateStats() {
        this.statNumbers.forEach((stat, index) => {
            const finalValue = stat.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
            const suffix = finalValue.replace(/[\d.]/g, '');

            if (!isNaN(numericValue)) {
                // Animate from 0 to final value
                let currentValue = 0;
                const increment = numericValue / 60;
                const duration = 2000; // 2 seconds
                const stepTime = duration / 60;

                setTimeout(() => {
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= numericValue) {
                            currentValue = numericValue;
                            clearInterval(timer);
                        }

                        if (suffix.includes('h')) {
                            stat.textContent = Math.floor(currentValue) + suffix;
                        } else {
                            stat.textContent = Math.floor(currentValue) + suffix;
                        }
                    }, stepTime);
                }, index * 300); // Stagger animations
            }
        });
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        this.libraryCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }

    handleSearch(query) {
        // Show/hide clear button
        if (query.trim()) {
            this.searchClear?.classList.remove('hidden');
        } else {
            this.searchClear?.classList.add('hidden');
        }

        // Debounce search
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        this.searchTimeout = setTimeout(() => {
            this.performSearch(query.trim());
        }, 300);
    }

    performSearch(query) {
        if (!query) {
            this.showAllCards();
            return;
        }

        this.libraryCards.forEach(card => {
            const title = card.querySelector('h3, .playlist-title, .song-title')?.textContent.toLowerCase() || '';
            const artist = card.querySelector('.artist-name, .playlist-artist')?.textContent.toLowerCase() || '';

            if (title.includes(query.toLowerCase()) || artist.includes(query.toLowerCase())) {
                card.style.display = 'block';
                card.classList.add('search-match');
            } else {
                card.style.display = 'none';
                card.classList.remove('search-match');
            }
        });

        this.announceAction(`Found ${document.querySelectorAll('.search-match').length} results for "${query}"`);
    }

    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
            this.searchClear?.classList.add('hidden');
            this.showAllCards();
            this.searchInput.focus();
            this.announceAction('Search cleared');
        }
    }

    showAllCards() {
        this.libraryCards.forEach(card => {
            card.style.display = 'block';
            card.classList.remove('search-match');
        });
    }

    switchSection(section) {
        // Update active tab
        this.navTabs.forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`)?.classList.add('active');

        // Show/hide sections (if implemented)
        this.announceAction(`Switched to ${section} section`);
    }

    shuffleAll() {
        this.announceAction('Shuffling all music in your library');
        // Simulate shuffle functionality
        console.log('Shuffling all music...');
    }

    importMusic() {
        this.announceAction('Opening music import dialog');
        // Simulate file import
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = true;
        input.accept = 'audio/*';
        input.onchange = (e) => {
            const files = e.target.files;
            this.announceAction(`Selected ${files.length} file(s) for import`);
        };
        input.click();
    }

    async createPlaylist() {
        const name = prompt('Enter a name for your new playlist:');
        if (!name) return;
        try {
            const res = await fetch('http://localhost:3001/api/library/playlists', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name })
            });
            if (res.ok) {
                this.announceAction('Playlist created!');
                this.fetchAndRenderLibrary();
            } else {
                this.announceAction('Failed to create playlist.');
            }
        } catch (e) {
            this.announceAction('Error creating playlist.');
        }
    }

    playAllLikedSongs() {
        this.announceAction('Playing all liked songs');
        // Simulate playing all liked songs
        console.log('Playing all liked songs...');
    }

    handleCardClick(_, card) {
        const cardType = card.classList.contains('playlist-card') ? 'playlist' :
                        card.classList.contains('liked-song-card') ? 'song' :
                        card.classList.contains('artist-card') ? 'artist' : 'library';

        const title = card.querySelector('h3, .playlist-title, .song-title')?.textContent || 'Unknown';

        this.announceAction(`Opening ${cardType}: ${title}`);

        switch(cardType) {
            case 'playlist':
                // Get playlist ID from edit/delete button or card data
                let playlistId = card.querySelector('.edit-playlist-btn, .delete-playlist-btn')?.getAttribute('data-id');
                if (!playlistId && card.dataset.id) playlistId = card.dataset.id;
                if (playlistId) this.openPlaylistModal(playlistId, title);
                break;
            case 'song':
                console.log(`Playing song: ${title}`);
                break;
            case 'artist':
                console.log(`Opening artist page: ${title}`);
                break;
            default:
                console.log(`Opening ${title}`);
        }
    }

    async openPlaylistModal(playlistId, playlistName) {
        const modal = document.getElementById('playlist-modal');
        const closeBtn = document.getElementById('close-playlist-modal');
        const titleElem = document.getElementById('modal-playlist-title');
        const trackList = document.getElementById('modal-track-list');
        titleElem.textContent = playlistName;
        trackList.innerHTML = '<li>Loading...</li>';
        modal.style.display = 'flex';
        // Fetch playlist details
        try {
            const res = await fetch(`http://localhost:3001/api/library/playlists/${playlistId}`);
            if (!res.ok) throw new Error('Not found');
            const playlist = await res.json();
            if (!playlist.tracks.length) {
                trackList.innerHTML = '<li>No songs in this playlist.</li>';
            } else {
                // Fetch track info for each track
                const tracksInfo = await Promise.all(playlist.tracks.map(id => this.fetchDeezerTrack(id)));
                trackList.innerHTML = '';
                tracksInfo.forEach((track, i) => {
                    const li = document.createElement('li');
                    if (track) {
                        li.innerHTML = `<span>${track.title} <span style='color:#aaa'>by ${track.artist.name}</span></span> <button data-index='${i}' data-id='${playlist.tracks[i]}'>Remove</button>`;
                    } else {
                        li.innerHTML = `<span>Track ID ${playlist.tracks[i]}</span> <button data-index='${i}' data-id='${playlist.tracks[i]}'>Remove</button>`;
                    }
                    trackList.appendChild(li);
                });
                // Remove song from playlist
                trackList.querySelectorAll('button').forEach(btn => {
                    btn.addEventListener('click', async () => {
                        await fetch(`http://localhost:3001/api/library/playlists/${playlistId}/tracks/${btn.dataset.id}`, { method: 'DELETE' });
                        this.openPlaylistModal(playlistId, playlistName); // Refresh
                        this.fetchAndRenderLibrary(); // Update main view
                    });
                });
            }
        } catch (e) {
            trackList.innerHTML = '<li>Error loading playlist.</li>';
        }
        // Close modal
        closeBtn.onclick = () => { modal.style.display = 'none'; };
        closeBtn.onkeydown = (e) => { if (e.key === 'Enter' || e.key === ' ') modal.style.display = 'none'; };
        window.onclick = (e) => { if (e.target === modal) modal.style.display = 'none'; };
    }

    createLiveRegion() {
        const liveRegion = document.createElement('div');
        liveRegion.id = 'aria-live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
        return liveRegion;
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;

            // Clear the message after a short delay
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }

    // Method to update library statistics
    updateStats(stats) {
        const elements = {
            totalPlaylists: document.getElementById('totalPlaylists'),
            totalLikedSongs: document.getElementById('totalLikedSongs'),
            totalListeningTime: document.getElementById('totalListeningTime'),
            totalArtists: document.getElementById('totalArtists')
        };

        Object.keys(stats).forEach(key => {
            if (elements[key]) {
                elements[key].textContent = stats[key];
            }
        });
    }

    // Method to add shimmer loading effect
    addLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.add('loading-shimmer');
        });
    }

    removeLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.remove('loading-shimmer');
        });
    }

    async fetchAndRenderLibrary() {
        // Fetch playlists
        let playlists = [];
        let likedSongs = [];
        try {
            const playlistsRes = await fetch('http://localhost:3001/api/library/playlists');
            playlists = await playlistsRes.json();
        } catch (e) {
            console.error('Failed to fetch playlists:', e);
        }
        try {
            const likedRes = await fetch('http://localhost:3001/api/library/liked');
            likedSongs = await likedRes.json();
        } catch (e) {
            console.error('Failed to fetch liked songs:', e);
        }
        this.renderPlaylists(playlists);
        this.renderLikedSongs(likedSongs);
        this.updateStats({
            totalPlaylists: playlists.length,
            totalLikedSongs: likedSongs.length,
        });
    }

    // Helper: fetch Deezer track info by ID
    fetchDeezerTrack(trackId) {
        return fetch(`https://corsproxy.io/?https://api.deezer.com/track/${trackId}`)
            .then(res => res.ok ? res.json() : null)
            .catch(() => null);
    }

    async renderPlaylists(playlists) {
        const container = document.querySelector('.library-cards.centered');
        if (!container) return;
        container.innerHTML = '';
        if (!playlists.length) {
            container.innerHTML = '<span class="empty-results">No playlists found.</span>';
            return;
        }
        for (const pl of playlists) {
            // Fetch track data for each track in the playlist
            let tracksInfo = [];
            if (pl.tracks && pl.tracks.length) {
                tracksInfo = await Promise.all(pl.tracks.map(id => this.fetchDeezerTrack(id)));
            }
            const card = document.createElement('div');
            card.className = 'card playlist-card';
            card.tabIndex = 0;
            card.setAttribute('aria-label', `${pl.name} playlist, ${pl.tracks.length} tracks`);
            card.dataset.id = pl.id; // <-- Add this for modal logic
            // Show up to 3 track covers in the playlist card
            const covers = tracksInfo.filter(t => t && t.album && t.album.cover_small).slice(0,3).map(t => `<img src="${t.album.cover_small}" alt="${t.title} Cover" loading="lazy">`).join('');
            // Show up to 2 track titles/artists with preview buttons
            const trackList = tracksInfo.filter(t => t && t.title && t.artist).slice(0,2).map(t => `
                <div class='playlist-track'>
                    ${t.title} <span class='playlist-artist'>by ${t.artist.name}</span>
                    <button class="preview-btn" data-preview="${t.preview}" aria-label="Play preview"><i class="fas fa-play"></i></button>
                </div>
            `).join('');
            card.innerHTML = `
                <div class="img-container">
                    ${covers || `<img src='imgs/album-01.png' alt='${pl.name} Cover' loading='lazy'>`}
                </div>
                <div class="card-content">
                    <h3 class="playlist-title">${pl.name}</h3>
                    <div class="playlist-meta">
                        <span class="playlist-count"><i class="fas fa-music"></i> ${pl.tracks.length} tracks</span>
                        <button class="edit-playlist-btn" data-id="${pl.id}" aria-label="Edit playlist"><i class="fas fa-edit"></i></button>
                        <button class="delete-playlist-btn" data-id="${pl.id}" aria-label="Delete playlist"><i class="fas fa-trash"></i></button>
                    </div>
                    <div class="playlist-tracks-preview">${trackList || '<span class=\'playlist-empty\'>No tracks yet</span>'}</div>
                </div>
            `;
            container.appendChild(card);
        }
        // Add preview button event listeners for playlist tracks
        container.querySelectorAll('.playlist-track .preview-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const url = btn.getAttribute('data-preview');
                if (url) {
                    this.playPreview(url, btn);
                } else {
                    this.announceAction('No preview available.');
                }
            });
        });
        // Add edit event listeners
        container.querySelectorAll('.edit-playlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = btn.getAttribute('data-id');
                this.editPlaylist(id);
            });
        });
        // Add delete event listeners
        container.querySelectorAll('.delete-playlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = btn.getAttribute('data-id');
                if (confirm('Delete this playlist?')) {
                    this.deletePlaylist(id);
                }
            });
        });
    }

    async editPlaylist(id) {
        const newName = prompt('Enter a new name for this playlist:');
        if (!newName) return;
        try {
            const res = await fetch(`http://localhost:3001/api/library/playlists/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name: newName })
            });
            if (res.ok) {
                this.announceAction('Playlist updated!');
                this.fetchAndRenderLibrary();
            } else {
                this.announceAction('Failed to update playlist.');
            }
        } catch (e) {
            this.announceAction('Error updating playlist.');
        }
    }

    async deletePlaylist(id) {
        try {
            const res = await fetch(`http://localhost:3001/api/library/playlists/${id}`, { method: 'DELETE' });
            if (res.ok) {
                this.announceAction('Playlist deleted!');
                this.fetchAndRenderLibrary();
            } else {
                this.announceAction('Failed to delete playlist.');
            }
        } catch (e) {
            this.announceAction('Error deleting playlist.');
        }
    }

    async renderLikedSongs(likedSongs) {
        const container = document.querySelector('.library-cards.liked-songs-cards');
        if (!container) return;
        container.innerHTML = '';
        if (!likedSongs.length) {
            container.innerHTML = '<span class="empty-results">No liked songs yet.</span>';
            return;
        }
        // Fetch all track info in parallel
        const trackData = await Promise.all(likedSongs.map(id => this.fetchDeezerTrack(id)));
        trackData.forEach((track, idx) => {
            const songId = likedSongs[idx];
            const card = document.createElement('div');
            card.className = 'card liked-song-card';
            card.tabIndex = 0;
            card.setAttribute('aria-label', track && track.title ? `${track.title} by ${track.artist.name}, liked` : `Song ID ${songId}, liked`);
            card.innerHTML = `
                <div class="img-container">
                    <img src="${track && track.album && track.album.cover_medium ? track.album.cover_medium : 'imgs/album-01.png'}" alt="${track && track.title ? track.title : 'Song'} Cover" loading="lazy">
                </div>
                <div class="card-content">
                    <h3 class="song-title">${track && track.title ? track.title : 'Song #' + songId}</h3>
                    <div class="artist-name">${track && track.artist && track.artist.name ? track.artist.name : 'Artist Unknown'}</div>
                    <button class="preview-btn" data-preview="${track && track.preview ? track.preview : ''}" aria-label="Play preview"><i class="fas fa-play"></i> Preview</button>
                    <button class="like-btn" data-songid="${songId}" aria-label="Like song"><i class="fas fa-heart"></i> Like</button>
                    <button class="remove-liked-btn" data-songid="${songId}" aria-label="Remove from liked"><i class="fas fa-trash"></i></button>
                </div>
            `;
            container.appendChild(card);
        });
        // Add preview button event listeners
        container.querySelectorAll('.preview-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const url = btn.getAttribute('data-preview');
                if (url) {
                    this.playPreview(url, btn);
                } else {
                    this.announceAction('No preview available.');
                }
            });
        });
        // Add like button event listeners
        container.querySelectorAll('.like-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const songId = parseInt(btn.getAttribute('data-songid'));
                this.likeSong(songId);
            });
        });
        // Add remove liked song event listeners
        container.querySelectorAll('.remove-liked-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const songId = btn.getAttribute('data-songid');
                if (confirm('Remove this song from liked?')) {
                    this.removeLikedSong(songId);
                }
            });
        });
    }

    playPreview(url, btn) {
        // Remove any existing preview audio
        if (this._previewAudio) {
            this._previewAudio.pause();
            this._previewAudio = null;
        }
        // Create and play new audio
        const audio = new Audio(url);
        audio.volume = 0.8;
        audio.play();
        this._previewAudio = audio;
        this.announceAction('Playing preview...');
        // Optionally, visually indicate playing
        btn.classList.add('playing');
        audio.onended = () => {
            btn.classList.remove('playing');
        };
        audio.onerror = () => {
            btn.classList.remove('playing');
            this.announceAction('Preview failed to play.');
        };
    }

    async removeLikedSong(songId) {
        try {
            const res = await fetch(`http://localhost:3001/api/library/liked/${songId}`, { method: 'DELETE' });
            if (res.ok) {
                this.announceAction('Song removed from liked!');
                this.fetchAndRenderLibrary();
            } else {
                this.announceAction('Failed to remove song.');
            }
        } catch (e) {
            this.announceAction('Error removing song.');
        }
    }
}

// Initialize the library manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.libraryManager = new LibraryManager();
    window.libraryManager.fetchAndRenderLibrary();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LibraryManager;
}