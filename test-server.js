// Simple test server
const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8080;

app.use(cors());
app.use(express.static('.'));
app.use(express.json());

// Simple test endpoint
app.get('/api/test', (req, res) => {
  res.json({ message: 'Server is working!' });
});

// Sample data (in-memory for demo)
let samplePlaylists = [
  { id: 1, name: 'Favorites', tracks: [101, 102, 104] },
  { id: 2, name: 'Chill Vibes', tracks: [103, 105] },
  { id: 3, name: 'Workout Mix', tracks: [106, 101] }
];

let likedSongs = [101, 102, 103];
let nextPlaylistId = 4;

const sampleTracks = {
  101: { id: 101, title: "Midnight Dreams", artist: "Luna Echo", album: "Neon Nights", duration: "3:45", cover: "imgs/album-01.png" },
  102: { id: 102, title: "Ocean Waves", artist: "Cosmic Drift", album: "Deep Space", duration: "4:12", cover: "imgs/album-02.png" },
  103: { id: 103, title: "City Lights", artist: "Neon Pulse", album: "Urban Vibes", duration: "3:28", cover: "imgs/album-03-B.png" },
  104: { id: 104, title: "Starfall", artist: "Galaxy Dreams", album: "Celestial", duration: "5:03", cover: "imgs/album-04-B.png" },
  105: { id: 105, title: "Electric Soul", artist: "Synth Wave", album: "Digital Hearts", duration: "4:21", cover: "imgs/album-01.png" },
  106: { id: 106, title: "Neon Rain", artist: "Cyber City", album: "Future Noir", duration: "3:56", cover: "imgs/album-02.png" }
};

// API endpoints
app.get('/api/library/playlists', (req, res) => {
  console.log('📋 Playlists requested');
  res.json(samplePlaylists);
});

app.get('/api/library/liked', (req, res) => {
  console.log('❤️ Liked songs requested');
  const likedTracksWithDetails = likedSongs.map(trackId => sampleTracks[trackId]).filter(Boolean);
  res.json(likedTracksWithDetails);
});

// Create new playlist
app.post('/api/library/playlists', (req, res) => {
  console.log('➕ Creating new playlist:', req.body.name);
  const newPlaylist = {
    id: nextPlaylistId++,
    name: req.body.name,
    tracks: []
  };
  samplePlaylists.push(newPlaylist);
  res.status(201).json(newPlaylist);
});

// Edit playlist name
app.put('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  console.log('✏️ Editing playlist:', id, 'to:', req.body.name);
  const playlist = samplePlaylists.find(p => p.id === id);
  if (playlist) {
    playlist.name = req.body.name;
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Delete playlist
app.delete('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  console.log('🗑️ Deleting playlist:', id);
  const index = samplePlaylists.findIndex(p => p.id === id);
  if (index !== -1) {
    samplePlaylists.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Get specific playlist
app.get('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  console.log('📋 Getting playlist details:', id);
  const playlist = samplePlaylists.find(p => p.id === id);
  if (playlist) {
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Remove track from playlist
app.delete('/api/library/playlists/:id/tracks/:trackId', (req, res) => {
  const playlistId = parseInt(req.params.id);
  const trackId = parseInt(req.params.trackId);
  console.log('🎵 Removing track', trackId, 'from playlist', playlistId);

  const playlist = samplePlaylists.find(p => p.id === playlistId);
  if (playlist) {
    const trackIndex = playlist.tracks.indexOf(trackId);
    if (trackIndex !== -1) {
      playlist.tracks.splice(trackIndex, 1);
      res.json({ success: true });
    } else {
      res.status(404).json({ error: 'Track not found in playlist' });
    }
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Remove liked song
app.delete('/api/library/liked/:songId', (req, res) => {
  const songId = parseInt(req.params.songId);
  console.log('💔 Removing liked song:', songId);
  const index = likedSongs.indexOf(songId);
  if (index !== -1) {
    likedSongs.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Song not found in liked songs' });
  }
});

app.listen(PORT, () => {
  console.log(`🎵 Test server running on port ${PORT}`);
  console.log(`📱 Open: http://localhost:${PORT}/library.html`);
  console.log(`🔧 API Test: http://localhost:${PORT}/api/test`);
});

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n👋 Server shutting down...');
  process.exit(0);
});
